#!/usr/bin/env python3
"""
Test script to verify persistent shell functionality
"""

import requests
import json
import time

def test_persistent_shell():
    """Test that shell state persists between commands"""
    base_url = 'http://localhost:8000'
    
    print("🧪 Testing Persistent Shell Functionality")
    print("=" * 50)
    
    # Test server connection
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Server is running (status: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Start it first with: ./start.sh")
        return False
    
    # Create session and login
    session = requests.Session()
    
    print("\n🔐 Logging in...")
    login_data = {'password': 'admin123'}
    response = session.post(f'{base_url}/login', data=login_data)
    
    if not response.json().get('success'):
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test persistent directory changes
    print("\n📁 Testing Directory Persistence...")
    
    # Get initial directory
    response = session.get(f'{base_url}/current-dir')
    initial_dir = response.json().get('current_dir', '~')
    print(f"Initial directory: {initial_dir}")
    
    # Create a test directory
    test_commands = [
        ('mkdir -p /tmp/webterm_test', 'Create test directory'),
        ('cd /tmp/webterm_test', 'Change to test directory'),
        ('pwd', 'Verify directory change'),
        ('touch test_file.txt', 'Create test file'),
        ('ls -la', 'List files in current directory'),
        ('echo "Hello World" > test_file.txt', 'Write to test file'),
        ('cat test_file.txt', 'Read test file content')
    ]
    
    for cmd, description in test_commands:
        print(f"\n📝 {description}")
        print(f"Command: {cmd}")
        
        command_data = {'command': cmd}
        response = session.post(f'{base_url}/execute', json=command_data)
        result = response.json()
        
        if result.get('success'):
            output = result.get('output', '').strip()
            if output:
                print(f"Output: {output}")
            print("✅ Success")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
    
    # Test environment variable persistence
    print("\n🌍 Testing Environment Variable Persistence...")
    
    env_commands = [
        ('export TEST_VAR="persistent_value"', 'Set environment variable'),
        ('echo $TEST_VAR', 'Check environment variable'),
        ('export PATH=$PATH:/tmp/webterm_test', 'Modify PATH'),
        ('echo $PATH | grep webterm_test', 'Verify PATH modification')
    ]
    
    for cmd, description in env_commands:
        print(f"\n📝 {description}")
        print(f"Command: {cmd}")
        
        command_data = {'command': cmd}
        response = session.post(f'{base_url}/execute', json=command_data)
        result = response.json()
        
        if result.get('success'):
            output = result.get('output', '').strip()
            if output:
                print(f"Output: {output}")
            print("✅ Success")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
    
    # Test that we're still in the test directory
    print("\n📍 Verifying Directory Persistence...")
    command_data = {'command': 'pwd'}
    response = session.post(f'{base_url}/execute', json=command_data)
    result = response.json()
    
    if result.get('success'):
        current_dir = result.get('output', '').strip()
        if '/tmp/webterm_test' in current_dir:
            print(f"✅ Directory persistence confirmed: {current_dir}")
        else:
            print(f"❌ Directory not persistent: {current_dir}")
    
    # Test background process (simple example)
    print("\n⚙️ Testing Background Process...")
    bg_commands = [
        ('sleep 2 &', 'Start background sleep process'),
        ('jobs', 'List background jobs'),
        ('ps aux | grep sleep | grep -v grep', 'Check if sleep process is running')
    ]
    
    for cmd, description in bg_commands:
        print(f"\n📝 {description}")
        print(f"Command: {cmd}")
        
        command_data = {'command': cmd}
        response = session.post(f'{base_url}/execute', json=command_data)
        result = response.json()
        
        if result.get('success'):
            output = result.get('output', '').strip()
            if output:
                print(f"Output: {output}")
            print("✅ Success")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
        
        # Small delay for background process test
        if 'sleep' in cmd:
            time.sleep(0.5)
    
    # Cleanup
    print("\n🧹 Cleaning up...")
    cleanup_commands = [
        ('cd ~', 'Return to home directory'),
        ('rm -rf /tmp/webterm_test', 'Remove test directory')
    ]
    
    for cmd, description in cleanup_commands:
        command_data = {'command': cmd}
        response = session.post(f'{base_url}/execute', json=command_data)
        result = response.json()
        print(f"✅ {description}")
    
    print("\n🎉 Persistent Shell Test Complete!")
    print("=" * 50)
    print("✅ Directory changes persist between commands")
    print("✅ Environment variables persist in session")
    print("✅ Background processes can be started")
    print("✅ Shell state is maintained across requests")
    
    return True

if __name__ == '__main__':
    test_persistent_shell()
