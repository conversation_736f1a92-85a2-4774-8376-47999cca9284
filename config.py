"""
Configuration file for Flask Web Terminal
"""

import os
from werkzeug.security import generate_password_hash

class Config:
    """Base configuration"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    ADMIN_PASSWORD_HASH = generate_password_hash(os.environ.get('ADMIN_PASSWORD', 'admin123'))
    
    # Server configuration
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 8000))
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    # Command policy
    ALLOW_ALL_COMMANDS = os.environ.get('ALLOW_ALL_COMMANDS', 'true').lower() == 'true'
    
    # Command execution settings
    COMMAND_TIMEOUT = int(os.environ.get('COMMAND_TIMEOUT', 30))
    MAX_OUTPUT_LENGTH = int(os.environ.get('MAX_OUTPUT_LENGTH', 10000))
    
    # Security settings
    SESSION_TIMEOUT = int(os.environ.get('SESSION_TIMEOUT', 3600))  # 1 hour
    MAX_COMMAND_LENGTH = int(os.environ.get('MAX_COMMAND_LENGTH', 1000))
    
    # Allowed commands - customize as needed
    ALLOWED_COMMANDS = [
        # File system
        'ls', 'pwd', 'cd', 'mkdir', 'rmdir', 'touch', 'rm', 'cp', 'mv',
        'chmod', 'chown', 'ln', 'readlink', 'realpath', 'basename', 'dirname',
        
        # File viewing/editing
        'cat', 'head', 'tail', 'less', 'more', 'grep', 'awk', 'sed', 'sort',
        'uniq', 'cut', 'tr', 'wc', 'diff', 'cmp',
        
        # System information
        'whoami', 'id', 'groups', 'date', 'uptime', 'uname', 'hostname',
        'env', 'printenv', 'which', 'whereis', 'type', 'history',
        
        # Process management
        'ps', 'top', 'htop', 'jobs', 'bg', 'fg', 'nohup', 'screen', 'tmux',
        'kill', 'killall', 'pgrep', 'pkill',
        
        # System resources
        'df', 'du', 'free', 'lscpu', 'lsmem', 'lsblk', 'lsusb', 'lspci',
        'mount', 'umount', 'lsof', 'fuser',
        
        # Network
        'ping', 'traceroute', 'nslookup', 'dig', 'host', 'curl', 'wget',
        'netstat', 'ss', 'ip', 'ifconfig', 'route', 'arp', 'nc', 'telnet',
        
        # Archive/compression
        'tar', 'gzip', 'gunzip', 'zip', 'unzip', 'compress', 'uncompress',
        
        # Text processing
        'find', 'locate', 'xargs', 'tee', 'split', 'join', 'paste', 'expand',
        'unexpand', 'fmt', 'fold', 'pr', 'nl',
        
        # System services (be careful with these)
        'systemctl', 'service', 'journalctl', 'dmesg', 'logger',
        
        # Development tools
        'git', 'svn', 'make', 'gcc', 'g++', 'python', 'python3', 'pip',
        'node', 'npm', 'yarn', 'ruby', 'gem', 'java', 'javac',
        
        # Container/virtualization
        'docker', 'docker-compose', 'kubectl', 'helm', 'vagrant',
        
        # Remote access
        'ssh', 'scp', 'sftp', 'rsync', 'rsh', 'rcp',
        
        # Security/permissions
        'sudo', 'su', 'passwd', 'chage', 'usermod', 'groupmod',
        'iptables', 'ufw', 'firewall-cmd',
        
        # Package management
        'apt', 'apt-get', 'dpkg', 'yum', 'dnf', 'rpm', 'zypper',
        'pacman', 'brew', 'snap', 'flatpak',
        
        # Monitoring/logging
        'tail', 'watch', 'iostat', 'vmstat', 'sar', 'mpstat', 'pidstat',
        'tcpdump', 'wireshark', 'tshark',
        
        # Miscellaneous
        'echo', 'printf', 'test', 'expr', 'bc', 'dc', 'factor', 'seq',
        'yes', 'true', 'false', 'sleep', 'timeout', 'time', 'strace',
        'ltrace', 'ldd', 'file', 'stat', 'md5sum', 'sha1sum', 'sha256sum'
    ]
    
    # Commands that should be run with extra caution
    DANGEROUS_COMMANDS = [
        'rm', 'rmdir', 'mv', 'chmod', 'chown', 'sudo', 'su', 'passwd',
        'iptables', 'ufw', 'systemctl', 'service', 'kill', 'killall'
    ]
    
    # Blocked command patterns (regex patterns)
    BLOCKED_PATTERNS = [
        r'rm\s+-rf\s+/',  # Prevent rm -rf /
        r':\(\)\{.*\}',   # Fork bomb pattern
        r'>\s*/dev/sd',   # Writing to disk devices
        r'dd\s+.*of=/dev', # DD to devices
    ]

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    # In production, these should be set via environment variables
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-must-set-secret-key'
    
    # More restrictive command list for production
    ALLOWED_COMMANDS = [
        'ls', 'pwd', 'cat', 'head', 'tail', 'grep', 'find', 'which',
        'whoami', 'id', 'date', 'uptime', 'uname', 'hostname',
        'ps', 'top', 'df', 'free', 'netstat', 'ss', 'ping'
    ]

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
