#!/usr/bin/env python3
"""
Production-ready startup script for Flask Web Terminal
"""

import os
import sys
import logging
from app import app
from config import config

def setup_logging():
    """Configure logging for the application"""
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('web-terminal.log') if os.environ.get('LOG_FILE') else logging.NullHandler()
        ]
    )

def validate_environment():
    """Validate required environment variables and settings"""
    warnings = []
    errors = []
    
    # Check for default password in production
    if os.environ.get('FLASK_ENV') == 'production':
        if os.environ.get('ADMIN_PASSWORD') == 'admin123' or not os.environ.get('ADMIN_PASSWORD'):
            errors.append("ADMIN_PASSWORD must be set to a secure value in production")
        
        if not os.environ.get('SECRET_KEY'):
            errors.append("SECRET_KEY must be set in production")
    
    # Check for default password in any environment
    if not os.environ.get('ADMIN_PASSWORD') or os.environ.get('ADMIN_PASSWORD') == 'admin123':
        warnings.append("Using default password 'admin123' - change ADMIN_PASSWORD for security")
    
    # Print warnings and errors
    for warning in warnings:
        logging.warning(f"⚠️  {warning}")
    
    for error in errors:
        logging.error(f"❌ {error}")
    
    if errors:
        sys.exit(1)

def main():
    """Main application entry point"""
    setup_logging()
    validate_environment()
    
    # Get configuration
    config_name = os.environ.get('FLASK_ENV', 'development')
    app_config = config.get(config_name, config['default'])
    
    # Configure app
    app.config.from_object(app_config)
    
    # Print startup information
    logging.info("🚀 Starting Flask Web Terminal")
    logging.info(f"📊 Environment: {config_name}")
    logging.info(f"🌐 Host: {app_config.HOST}")
    logging.info(f"🔌 Port: {app_config.PORT}")
    logging.info(f"🐛 Debug: {app_config.DEBUG}")
    logging.info(f"⏱️  Command timeout: {app_config.COMMAND_TIMEOUT}s")
    logging.info(f"🔒 Allowed commands: {len(app_config.ALLOWED_COMMANDS)}")
    
    if config_name == 'development':
        logging.info("🔑 Default password: admin123")
    
    # Create required directories
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    try:
        # Run the application
        app.run(
            host=app_config.HOST,
            port=app_config.PORT,
            debug=app_config.DEBUG,
            threaded=True
        )
    except KeyboardInterrupt:
        logging.info("👋 Shutting down Flask Web Terminal")
    except Exception as e:
        logging.error(f"💥 Failed to start application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
