# Flask Web Terminal

A secure web-based terminal interface that allows you to run SSH commands and system commands through a web browser. This application provides a terminal-like experience with command history, quick commands, and real-time output.

## Features

- **Web-based Terminal Interface**: Clean, responsive terminal UI with syntax highlighting
- **Command Execution**: Run system commands with real-time output
- **Security**: Whitelist-based command filtering and authentication
- **Command History**: Navigate through previous commands with arrow keys
- **Quick Commands**: Pre-defined buttons for common operations
- **System Information**: Display basic system info (hostname, uptime, etc.)
- **Session Management**: Simple password-based authentication
- **Responsive Design**: Works on desktop and mobile devices

## Security Features

- **Command Whitelist**: Only pre-approved commands can be executed
- **Authentication**: Password protection for terminal access
- **Timeout Protection**: Commands automatically timeout after 30 seconds
- **Limited Privileges**: Commands run with the same privileges as the web server
- **Input Sanitization**: All user input is properly escaped and validated

## Installation

1. **Clone or download the application files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set environment variables** (optional):
   ```bash
   export ADMIN_PASSWORD="your_secure_password"
   export SECRET_KEY="your_secret_key"
   export FLASK_DEBUG="False"
   export PORT="5000"
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

5. **Access the web terminal**:
   Open your browser and go to `http://localhost:5000`

## Default Credentials

- **Username**: Not required
- **Password**: `admin123` (change via `ADMIN_PASSWORD` environment variable)

## Allowed Commands

The application includes a whitelist of safe commands:

### System Information
- `ls`, `pwd`, `whoami`, `date`, `uptime`, `df`, `free`, `ps`
- `uname`, `hostname`, `id`, `groups`, `env`, `printenv`
- `lscpu`, `lsusb`, `lsblk`, `mount`, `lsof`

### Network Tools
- `netstat`, `ss`, `ip`, `ifconfig`, `ping`, `traceroute`
- `nslookup`, `dig`, `curl`, `wget`

### File Operations
- `cat`, `head`, `tail`, `grep`, `find`, `which`, `whereis`

### System Management
- `systemctl`, `service`, `journalctl`, `dmesg`, `iptables`
- `top`, `htop`, `history`

### Development Tools
- `git`, `docker`, `kubectl`, `ssh`, `scp`, `rsync`

## Usage

### Basic Commands
1. Log in with the admin password
2. Type commands in the input field and press Enter
3. Use arrow keys (↑/↓) to navigate command history
4. Click on history items to reuse commands
5. Use quick command buttons for common operations

### Keyboard Shortcuts
- **Enter**: Execute command
- **↑/↓**: Navigate command history
- **Ctrl+L**: Clear terminal
- **Ctrl+C**: Clear current input

### API Endpoints

The application also provides REST API endpoints:

- `POST /execute`: Execute a command
- `GET /allowed-commands`: Get list of allowed commands
- `GET /system-info`: Get basic system information
- `POST /login`: Authenticate user
- `GET /logout`: Logout user

## Configuration

### Environment Variables

- `ADMIN_PASSWORD`: Set the admin password (default: "admin123")
- `SECRET_KEY`: Flask secret key for sessions (auto-generated if not set)
- `PORT`: Port to run the application on (default: 5000)
- `FLASK_DEBUG`: Enable debug mode (default: False)

### Customizing Allowed Commands

Edit the `ALLOWED_COMMANDS` list in `app.py` to add or remove commands:

```python
ALLOWED_COMMANDS = [
    'ls', 'pwd', 'whoami', 'date', 'uptime',
    # Add your custom commands here
    'your_custom_command'
]
```

## Deployment

### Production Deployment

For production deployment, consider:

1. **Use a proper WSGI server** (e.g., Gunicorn):
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

2. **Set secure environment variables**:
   ```bash
   export ADMIN_PASSWORD="very_secure_password"
   export SECRET_KEY="random_secret_key"
   export FLASK_DEBUG="False"
   ```

3. **Use HTTPS** with a reverse proxy (nginx, Apache)

4. **Implement proper authentication** (replace simple password with OAuth, LDAP, etc.)

### Docker Deployment

Create a `Dockerfile`:
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

Build and run:
```bash
docker build -t web-terminal .
docker run -p 5000:5000 -e ADMIN_PASSWORD=secure_password web-terminal
```

## Security Considerations

⚠️ **Important Security Notes**:

1. **Change the default password** before deployment
2. **Use HTTPS** in production environments
3. **Restrict network access** to trusted users only
4. **Monitor command execution** and logs
5. **Keep the command whitelist minimal** - only include necessary commands
6. **Run with minimal privileges** - don't run as root
7. **Consider using containers** for additional isolation

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the application has appropriate permissions
2. **Command Not Found**: Check if the command is in the whitelist and available on the system
3. **Connection Refused**: Verify the port is not blocked by firewall
4. **Authentication Failed**: Check the password and session configuration

### Logs

The application logs command executions and errors. Check the console output for debugging information.

## License

This project is open source. Use at your own risk and ensure proper security measures are in place for production deployments.
