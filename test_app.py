#!/usr/bin/env python3
"""
Test script for Flask Web Terminal
"""

import unittest
import json
import tempfile
import os
from app import app

class WebTerminalTestCase(unittest.TestCase):
    """Test cases for the web terminal application"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = app.test_client()
        self.app.testing = True
        
        # Create a test session
        with self.app.session_transaction() as sess:
            sess['authenticated'] = True
    
    def test_index_redirect_when_not_authenticated(self):
        """Test that unauthenticated users are redirected to login"""
        with self.app.session_transaction() as sess:
            sess.pop('authenticated', None)
        
        response = self.app.get('/')
        self.assertIn(b'Terminal Access', response.data)
    
    def test_index_shows_terminal_when_authenticated(self):
        """Test that authenticated users see the terminal"""
        response = self.app.get('/')
        self.assertIn(b'Web Terminal', response.data)
        self.assertIn(b'terminalContainer', response.data)
    
    def test_login_with_correct_password(self):
        """Test login with correct password"""
        response = self.app.post('/login', data={'password': 'admin123'})
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    def test_login_with_incorrect_password(self):
        """Test login with incorrect password"""
        response = self.app.post('/login', data={'password': 'wrongpassword'})
        data = json.loads(response.data)
        self.assertFalse(data['success'])
    
    def test_execute_allowed_command(self):
        """Test executing an allowed command"""
        response = self.app.post('/execute',
                                data=json.dumps({'command': 'whoami'}),
                                content_type='application/json')
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('output', data)
    
    def test_execute_disallowed_command(self):
        """Test executing a disallowed command"""
        response = self.app.post('/execute',
                                data=json.dumps({'command': 'forbidden_command'}),
                                content_type='application/json')
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('not allowed', data['error'])
    
    def test_execute_without_authentication(self):
        """Test executing command without authentication"""
        with self.app.session_transaction() as sess:
            sess.pop('authenticated', None)
        
        response = self.app.post('/execute',
                                data=json.dumps({'command': 'whoami'}),
                                content_type='application/json')
        self.assertEqual(response.status_code, 401)
    
    def test_allowed_commands_endpoint(self):
        """Test the allowed commands endpoint"""
        response = self.app.get('/allowed-commands')
        data = json.loads(response.data)
        self.assertIn('commands', data)
        self.assertIsInstance(data['commands'], list)
        self.assertIn('whoami', data['commands'])
    
    def test_system_info_endpoint(self):
        """Test the system info endpoint"""
        response = self.app.get('/system-info')
        data = json.loads(response.data)
        self.assertIn('hostname', data)
        self.assertIn('whoami', data)
    
    def test_logout(self):
        """Test logout functionality"""
        response = self.app.get('/logout')
        self.assertIn(b'Terminal Access', response.data)

class CommandExecutionTestCase(unittest.TestCase):
    """Test cases for command execution functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        from app import execute_command, is_command_allowed
        self.execute_command = execute_command
        self.is_command_allowed = is_command_allowed
    
    def test_is_command_allowed_with_allowed_command(self):
        """Test command allowlist with allowed command"""
        self.assertTrue(self.is_command_allowed('ls'))
        self.assertTrue(self.is_command_allowed('ls -la'))
        self.assertTrue(self.is_command_allowed('whoami'))
    
    def test_is_command_allowed_with_disallowed_command(self):
        """Test command allowlist with disallowed command"""
        self.assertFalse(self.is_command_allowed('malicious_command'))
        self.assertFalse(self.is_command_allowed(''))
    
    def test_execute_simple_command(self):
        """Test executing a simple command"""
        result = self.execute_command('echo "test"')
        self.assertTrue(result['success'])
        self.assertIn('test', result['output'])
        self.assertEqual(result['exit_code'], 0)
    
    def test_execute_command_with_error(self):
        """Test executing a command that produces an error"""
        result = self.execute_command('ls /nonexistent_directory')
        # Command should execute but may have error output
        if result['success']:
            # If ls is allowed, it should execute but may have stderr
            self.assertIsNotNone(result['error'])
        else:
            # If ls is not allowed
            self.assertIn('not allowed', result['error'])

def run_integration_tests():
    """Run integration tests against a running server"""
    import requests
    import time
    
    base_url = 'http://localhost:5000'
    
    print("🧪 Running integration tests...")
    
    try:
        # Test if server is running
        response = requests.get(base_url, timeout=5)
        print(f"✅ Server is running (status: {response.status_code})")
        
        # Test login
        login_data = {'password': 'admin123'}
        session = requests.Session()
        response = session.post(f'{base_url}/login', data=login_data)
        
        if response.json().get('success'):
            print("✅ Login successful")
            
            # Test command execution
            command_data = {'command': 'whoami'}
            response = session.post(f'{base_url}/execute', json=command_data)
            
            if response.json().get('success'):
                print("✅ Command execution successful")
            else:
                print("❌ Command execution failed")
        else:
            print("❌ Login failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Start the server first with: python app.py")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'integration':
        run_integration_tests()
    else:
        # Run unit tests
        print("🧪 Running unit tests...")
        unittest.main(verbosity=2)
