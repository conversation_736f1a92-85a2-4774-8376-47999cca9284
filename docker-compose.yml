version: '3.8'

services:
  web-terminal:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - ADMIN_PASSWORD=change_this_password
      - SECRET_KEY=change_this_secret_key
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - web-terminal
    restart: unless-stopped
    profiles:
      - with-nginx

volumes:
  logs:
