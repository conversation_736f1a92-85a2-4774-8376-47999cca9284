#!/usr/bin/env python3
"""
Demo script to show the Flask Web Terminal functionality
"""

import requests
import json
import time
import sys

def demo_terminal():
    """Demonstrate the web terminal functionality"""
    base_url = 'http://localhost:8000'
    
    print("🚀 Flask Web Terminal Demo")
    print("=" * 40)
    
    # Test server connection
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Server is running (status: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Start it first with: ./start.sh")
        return False
    
    # Create session and login
    session = requests.Session()
    
    print("\n🔐 Testing Authentication...")
    login_data = {'password': 'admin123'}
    response = session.post(f'{base_url}/login', data=login_data)
    
    if not response.json().get('success'):
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test command policy
    print("\n📋 Checking Command Policy...")
    response = session.get(f'{base_url}/allowed-commands')
    policy_data = response.json()
    
    print(f"Policy: {policy_data['policy'].upper()}")
    print(f"Message: {policy_data['message']}")
    
    if policy_data['policy'] == 'blocklist':
        print(f"Blocked commands: {', '.join(policy_data['blocked_commands'][:5])}...")
    else:
        print(f"Allowed commands: {', '.join(policy_data['commands'][:10])}...")
    
    # Test various commands
    print("\n🧪 Testing Commands...")
    
    test_commands = [
        ('whoami', 'Get current user'),
        ('pwd', 'Show current directory'),
        ('ls -la', 'List files with details'),
        ('date', 'Show current date/time'),
        ('uptime', 'Show system uptime'),
        ('echo "Hello from Web Terminal!"', 'Echo test'),
        ('ps aux | head -5', 'Show processes (limited)'),
        ('env | grep USER', 'Show user environment'),
        ('which python3', 'Find python3 location'),
        ('uname -a', 'Show system information')
    ]
    
    successful_commands = 0
    
    for cmd, description in test_commands:
        print(f"\n📝 {description}")
        print(f"Command: {cmd}")
        
        command_data = {'command': cmd}
        response = session.post(f'{base_url}/execute', json=command_data)
        result = response.json()
        
        if result.get('success'):
            output = result.get('output', '').strip()
            if output:
                # Limit output display
                lines = output.split('\n')
                if len(lines) > 3:
                    print(f"Output: {lines[0]}")
                    print(f"        {lines[1]}")
                    print(f"        ... ({len(lines)} total lines)")
                else:
                    print(f"Output: {output}")
            else:
                print("Output: (no output)")
            
            if result.get('error'):
                print(f"Stderr: {result['error'].strip()}")
            
            successful_commands += 1
            print("✅ Success")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
    
    # Test blocked command
    print(f"\n🚫 Testing Blocked Command...")
    print("Command: rm -rf /tmp/nonexistent")
    
    command_data = {'command': 'rm -rf /tmp/nonexistent'}
    response = session.post(f'{base_url}/execute', json=command_data)
    result = response.json()
    
    if not result.get('success'):
        print(f"✅ Correctly blocked: {result.get('error')}")
    else:
        print("⚠️  Command was allowed (check your security settings)")
    
    # Test system info
    print(f"\n📊 System Information...")
    response = session.get(f'{base_url}/system-info')
    sys_info = response.json()
    
    for key, value in sys_info.items():
        print(f"{key.capitalize()}: {value}")
    
    # Summary
    print(f"\n📈 Demo Summary")
    print("=" * 40)
    print(f"✅ Successful commands: {successful_commands}/{len(test_commands)}")
    print(f"🔒 Security: {'Blocklist' if policy_data['policy'] == 'blocklist' else 'Whitelist'} mode")
    print(f"🌐 Access URL: {base_url}")
    print(f"🔑 Default password: admin123")
    
    return True

def interactive_demo():
    """Interactive demo mode"""
    base_url = 'http://localhost:8000'
    
    print("🎮 Interactive Web Terminal Demo")
    print("=" * 40)
    print("This will let you test commands interactively.")
    print("Type 'quit' to exit.\n")
    
    # Login
    session = requests.Session()
    login_data = {'password': 'admin123'}
    response = session.post(f'{base_url}/login', data=login_data)
    
    if not response.json().get('success'):
        print("❌ Login failed")
        return
    
    print("✅ Logged in successfully\n")
    
    while True:
        try:
            cmd = input("web-terminal> ").strip()
            
            if cmd.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not cmd:
                continue
            
            command_data = {'command': cmd}
            response = session.post(f'{base_url}/execute', json=command_data)
            result = response.json()
            
            if result.get('success'):
                if result.get('output'):
                    print(result['output'])
                if result.get('error'):
                    print(f"stderr: {result['error']}", file=sys.stderr)
            else:
                print(f"Error: {result.get('error', 'Unknown error')}", file=sys.stderr)
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        interactive_demo()
    else:
        demo_terminal()
