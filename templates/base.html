<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Web Terminal{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
        }
        
        .terminal-container {
            background-color: #000000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            min-height: 500px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .terminal-output {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .command-input {
            background-color: #000000;
            border: none;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            width: 100%;
            outline: none;
        }
        
        .command-input:focus {
            box-shadow: none;
            border: none;
            outline: none;
        }
        
        .prompt {
            color: #00ff00;
            font-weight: bold;
        }
        
        .error {
            color: #ff4444;
        }
        
        .success {
            color: #44ff44;
        }
        
        .info {
            color: #4444ff;
        }
        
        .navbar-dark {
            background-color: #2d2d2d !important;
        }
        
        .btn-terminal {
            background-color: #333;
            border-color: #555;
            color: #fff;
        }
        
        .btn-terminal:hover {
            background-color: #555;
            border-color: #777;
            color: #fff;
        }
        
        .system-info {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .command-history {
            max-height: 200px;
            overflow-y: auto;
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 10px;
        }
        
        .history-item {
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .history-item:hover {
            background-color: #444;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-terminal"></i> Web Terminal
            </a>
            <div class="navbar-nav ms-auto">
                {% if session.authenticated %}
                <a class="nav-link" href="/logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
