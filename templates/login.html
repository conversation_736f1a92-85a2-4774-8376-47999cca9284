{% extends "base.html" %}

{% block title %}Login - Web Terminal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card mt-5" style="background-color: #2d2d2d; border-color: #555;">
            <div class="card-header text-center">
                <h4><i class="fas fa-lock"></i> Terminal Access</h4>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password:</label>
                        <input type="password" class="form-control" id="password" name="password" 
                               style="background-color: #1a1a1a; border-color: #555; color: #fff;" required>
                    </div>
                    <button type="submit" class="btn btn-terminal w-100">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>
                <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
            </div>
            <div class="card-footer text-center text-muted">
                <small>Default password: admin123</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#loginForm').on('submit', function(e) {
        e.preventDefault();
        
        const password = $('#password').val();
        
        $.ajax({
            url: '/login',
            method: 'POST',
            data: { password: password },
            success: function(response) {
                if (response.success) {
                    window.location.href = '/';
                } else {
                    $('#loginError').text(response.error || 'Login failed').show();
                }
            },
            error: function() {
                $('#loginError').text('Connection error').show();
            }
        });
    });
    
    // Focus on password field
    $('#password').focus();
});
</script>
{% endblock %}
