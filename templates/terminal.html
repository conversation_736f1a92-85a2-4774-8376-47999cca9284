{% extends "base.html" %}

{% block title %}Web Terminal{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Terminal -->
            <div class="terminal-container" id="terminalContainer">
                <div id="terminalOutput" class="terminal-output"></div>
                <div class="d-flex align-items-center">
                    <span class="prompt" id="promptText">user@system:~$ </span>
                    <input type="text" id="commandInput" class="command-input" placeholder="Enter command..." autocomplete="off">
                </div>
            </div>

            <!-- Command Controls -->
            <div class="row mt-3">
                <div class="col-md-6">
                    <button id="clearBtn" class="btn btn-terminal">
                        <i class="fas fa-trash"></i> Clear Terminal
                    </button>
                    <button id="helpBtn" class="btn btn-terminal ms-2">
                        <i class="fas fa-question-circle"></i> Help
                    </button>
                </div>
                <div class="col-md-6 text-end">
                    <button id="commandPolicyBtn" class="btn btn-terminal">
                        <i class="fas fa-shield-alt"></i> Command Policy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background-color: #2d2d2d; color: #fff;">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-question-circle"></i> Web Terminal Help</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Usage:</h6>
                <ul>
                    <li>Type commands in the input field and press Enter</li>
                    <li>Use Up/Down arrow keys to navigate command history</li>
                    <li>Type <code>clear</code> or <code>cls</code> to clear the terminal</li>
                    <li>Navigate directories with <code>cd</code> - changes persist</li>
                    <li>Set environment variables - they persist in your session</li>
                    <li>Most standard Unix/Linux commands are supported</li>
                </ul>

                <h6>Persistent Shell:</h6>
                <ul>
                    <li>Each user gets a persistent bash shell session</li>
                    <li>Directory changes with <code>cd</code> are maintained</li>
                    <li>Environment variables persist across commands</li>
                    <li>Background processes continue running</li>
                </ul>

                <h6>Security:</h6>
                <ul>
                    <li>Dangerous commands are blocked for safety</li>
                    <li>Commands run with current user privileges</li>
                    <li>30-second timeout for all commands</li>
                    <li>Input validation and pattern blocking</li>
                </ul>
                
                <h6>Keyboard Shortcuts:</h6>
                <ul>
                    <li><kbd>Ctrl+L</kbd> - Clear terminal</li>
                    <li><kbd>Ctrl+C</kbd> - Clear current input</li>
                    <li><kbd>Up/Down</kbd> - Navigate history</li>
                    <li><kbd>clear</kbd> or <kbd>cls</kbd> - Clear terminal</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let commandHistory = [];
let historyIndex = -1;

$(document).ready(function() {
    $('#commandInput').focus();
    updatePrompt();

    // Command input handling
    $('#commandInput').on('keydown', function(e) {
        if (e.key === 'Enter') {
            executeCommand();
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateHistory(-1);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateHistory(1);
        } else if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            clearTerminal();
        } else if (e.ctrlKey && e.key === 'c') {
            e.preventDefault();
            $(this).val('');
        }
    });

    // Button handlers
    $('#clearBtn').click(clearTerminal);
    $('#helpBtn').click(() => $('#helpModal').modal('show'));
    $('#commandPolicyBtn').click(showCommandPolicy);
});

function updatePrompt() {
    $.get('/current-dir')
        .done(function(data) {
            const shortDir = data.current_dir.replace(new RegExp('^' + data.current_dir.split('/').slice(0, 3).join('/')), '~');
            $('#promptText').text(`${data.user}@${data.hostname}:${shortDir}$ `);
        })
        .fail(function() {
            $('#promptText').text('user@system:~$ ');
        });
}

function executeCommand() {
    const command = $('#commandInput').val().trim();
    if (!command) return;

    // Handle local commands
    if (command.toLowerCase() === 'clear' || command.toLowerCase() === 'cls') {
        clearTerminal();
        $('#commandInput').val('');
        return;
    }

    // Add to history
    if (commandHistory[commandHistory.length - 1] !== command) {
        commandHistory.push(command);
    }
    historyIndex = -1;

    // Display command in terminal with current prompt
    const currentPrompt = $('#promptText').text();
    appendToTerminal(`<span class="prompt">${escapeHtml(currentPrompt)}</span>${escapeHtml(command)}`);

    // Clear input
    $('#commandInput').val('');

    // Show loading
    const loadingId = 'loading-' + Date.now();
    appendToTerminal(`<span id="${loadingId}" class="info">Executing...</span>`);

    // Execute command
    $.ajax({
        url: '/execute',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ command: command }),
        success: function(response) {
            $(`#${loadingId}`).remove();

            if (response.success) {
                if (response.output) {
                    appendToTerminal(`<span class="success">${escapeHtml(response.output)}</span>`);
                }
                if (response.error) {
                    appendToTerminal(`<span class="error">${escapeHtml(response.error)}</span>`);
                }

                // Update prompt with new directory
                updatePrompt();
            } else {
                appendToTerminal(`<span class="error">Error: ${escapeHtml(response.error)}</span>`);
            }

            appendToTerminal(''); // Empty line
        },
        error: function() {
            $(`#${loadingId}`).remove();
            appendToTerminal('<span class="error">Connection error</span>');
            appendToTerminal(''); // Empty line
        }
    });
}

function appendToTerminal(text) {
    const output = $('#terminalOutput');
    output.append(text + '\n');
    
    // Scroll to bottom
    const container = $('#terminalContainer');
    container.scrollTop(container[0].scrollHeight);
}

function clearTerminal() {
    $('#terminalOutput').empty();
    updatePrompt();
    $('#commandInput').focus();
}

function navigateHistory(direction) {
    if (commandHistory.length === 0) return;

    if (direction === -1) { // Up
        if (historyIndex === -1) {
            historyIndex = commandHistory.length - 1;
        } else if (historyIndex > 0) {
            historyIndex--;
        }
    } else { // Down
        if (historyIndex === -1) return;
        if (historyIndex < commandHistory.length - 1) {
            historyIndex++;
        } else {
            historyIndex = -1;
            $('#commandInput').val('');
            return;
        }
    }

    $('#commandInput').val(commandHistory[historyIndex]);
}

function showCommandPolicy() {
    $.get('/allowed-commands')
        .done(function(data) {
            let message = `Command Policy: ${data.policy.toUpperCase()}\n\n${data.message}\n\n`;

            if (data.policy === 'whitelist' && data.commands) {
                message += `Allowed commands:\n${data.commands.join(', ')}`;
            } else if (data.policy === 'blocklist') {
                message += `Blocked commands:\n${data.blocked_commands.join(', ')}\n\n`;
                message += `Note: Most commands are allowed. Only dangerous commands are blocked.`;
            }

            alert(message);
        })
        .fail(function() {
            alert('Failed to load command policy');
        });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
