{% extends "base.html" %}

{% block title %}Web Terminal{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-9">
        <!-- System Info -->
        <div id="systemInfo" class="system-info">
            <h6><i class="fas fa-info-circle"></i> System Information</h6>
            <div id="systemInfoContent">Loading...</div>
        </div>
        
        <!-- Terminal -->
        <div class="terminal-container" id="terminalContainer">
            <div id="terminalOutput" class="terminal-output"></div>
            <div class="d-flex align-items-center">
                <span class="prompt" id="promptText">user@system:~$ </span>
                <input type="text" id="commandInput" class="command-input" placeholder="Enter command..." autocomplete="off">
            </div>
        </div>
        
        <!-- Command Controls -->
        <div class="row mt-3">
            <div class="col-md-6">
                <button id="clearBtn" class="btn btn-terminal">
                    <i class="fas fa-trash"></i> Clear Terminal
                </button>
                <button id="helpBtn" class="btn btn-terminal ms-2">
                    <i class="fas fa-question-circle"></i> Help
                </button>
            </div>
            <div class="col-md-6 text-end">
                <button id="allowedCommandsBtn" class="btn btn-terminal">
                    <i class="fas fa-list"></i> Allowed Commands
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3">
        <!-- Command History -->
        <div class="card" style="background-color: #2d2d2d; border-color: #555;">
            <div class="card-header">
                <h6><i class="fas fa-history"></i> Command History</h6>
            </div>
            <div class="card-body p-2">
                <div id="commandHistory" class="command-history">
                    <div class="text-muted text-center">No commands yet</div>
                </div>
                <button id="clearHistoryBtn" class="btn btn-sm btn-terminal mt-2 w-100">
                    Clear History
                </button>
            </div>
        </div>
        
        <!-- Quick Commands -->
        <div class="card mt-3" style="background-color: #2d2d2d; border-color: #555;">
            <div class="card-header">
                <h6><i class="fas fa-bolt"></i> Quick Commands</h6>
            </div>
            <div class="card-body p-2">
                <div class="d-grid gap-2">
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="ls -la">ls -la</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="pwd">pwd</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="whoami">whoami</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="uptime">uptime</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="df -h">df -h</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="free -h">free -h</button>
                    <button class="btn btn-sm btn-terminal quick-cmd" data-cmd="ps aux">ps aux</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background-color: #2d2d2d; color: #fff;">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-question-circle"></i> Web Terminal Help</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Usage:</h6>
                <ul>
                    <li>Type commands in the input field and press Enter</li>
                    <li>Use Up/Down arrow keys to navigate command history</li>
                    <li>Click on history items to reuse commands</li>
                    <li>Use quick command buttons for common operations</li>
                </ul>
                
                <h6>Security:</h6>
                <ul>
                    <li>Only whitelisted commands are allowed</li>
                    <li>Commands run with limited privileges</li>
                    <li>30-second timeout for all commands</li>
                </ul>
                
                <h6>Keyboard Shortcuts:</h6>
                <ul>
                    <li><kbd>Ctrl+L</kbd> - Clear terminal</li>
                    <li><kbd>Ctrl+C</kbd> - Clear current input</li>
                    <li><kbd>Up/Down</kbd> - Navigate history</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let commandHistory = [];
let historyIndex = -1;

$(document).ready(function() {
    loadSystemInfo();
    $('#commandInput').focus();
    
    // Command input handling
    $('#commandInput').on('keydown', function(e) {
        if (e.key === 'Enter') {
            executeCommand();
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateHistory(-1);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateHistory(1);
        } else if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            clearTerminal();
        } else if (e.ctrlKey && e.key === 'c') {
            e.preventDefault();
            $(this).val('');
        }
    });
    
    // Button handlers
    $('#clearBtn').click(clearTerminal);
    $('#helpBtn').click(() => $('#helpModal').modal('show'));
    $('#allowedCommandsBtn').click(showAllowedCommands);
    $('#clearHistoryBtn').click(clearHistory);
    
    // Quick command buttons
    $('.quick-cmd').click(function() {
        const cmd = $(this).data('cmd');
        $('#commandInput').val(cmd);
        executeCommand();
    });
    
    // History item clicks
    $(document).on('click', '.history-item', function() {
        const cmd = $(this).text();
        $('#commandInput').val(cmd);
        $('#commandInput').focus();
    });
});

function loadSystemInfo() {
    $.get('/system-info')
        .done(function(data) {
            let info = '';
            for (const [key, value] of Object.entries(data)) {
                info += `<strong>${key}:</strong> ${value}<br>`;
            }
            $('#systemInfoContent').html(info);
        })
        .fail(function() {
            $('#systemInfoContent').html('<span class="error">Failed to load system info</span>');
        });
}

function executeCommand() {
    const command = $('#commandInput').val().trim();
    if (!command) return;
    
    // Add to history
    if (commandHistory[commandHistory.length - 1] !== command) {
        commandHistory.push(command);
        updateHistoryDisplay();
    }
    historyIndex = -1;
    
    // Display command in terminal
    appendToTerminal(`<span class="prompt">$ </span>${escapeHtml(command)}`);
    
    // Clear input
    $('#commandInput').val('');
    
    // Show loading
    const loadingId = 'loading-' + Date.now();
    appendToTerminal(`<span id="${loadingId}" class="info">Executing...</span>`);
    
    // Execute command
    $.ajax({
        url: '/execute',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ command: command }),
        success: function(response) {
            $(`#${loadingId}`).remove();
            
            if (response.success) {
                if (response.output) {
                    appendToTerminal(`<span class="success">${escapeHtml(response.output)}</span>`);
                }
                if (response.error) {
                    appendToTerminal(`<span class="error">${escapeHtml(response.error)}</span>`);
                }
            } else {
                appendToTerminal(`<span class="error">Error: ${escapeHtml(response.error)}</span>`);
            }
            
            appendToTerminal(''); // Empty line
        },
        error: function() {
            $(`#${loadingId}`).remove();
            appendToTerminal('<span class="error">Connection error</span>');
            appendToTerminal(''); // Empty line
        }
    });
}

function appendToTerminal(text) {
    const output = $('#terminalOutput');
    output.append(text + '\n');
    
    // Scroll to bottom
    const container = $('#terminalContainer');
    container.scrollTop(container[0].scrollHeight);
}

function clearTerminal() {
    $('#terminalOutput').empty();
    $('#commandInput').focus();
}

function navigateHistory(direction) {
    if (commandHistory.length === 0) return;
    
    if (direction === -1) { // Up
        if (historyIndex === -1) {
            historyIndex = commandHistory.length - 1;
        } else if (historyIndex > 0) {
            historyIndex--;
        }
    } else { // Down
        if (historyIndex === -1) return;
        if (historyIndex < commandHistory.length - 1) {
            historyIndex++;
        } else {
            historyIndex = -1;
            $('#commandInput').val('');
            return;
        }
    }
    
    $('#commandInput').val(commandHistory[historyIndex]);
}

function updateHistoryDisplay() {
    const historyDiv = $('#commandHistory');
    if (commandHistory.length === 0) {
        historyDiv.html('<div class="text-muted text-center">No commands yet</div>');
        return;
    }
    
    let html = '';
    const recentCommands = commandHistory.slice(-10).reverse(); // Show last 10
    recentCommands.forEach(cmd => {
        html += `<div class="history-item">${escapeHtml(cmd)}</div>`;
    });
    historyDiv.html(html);
}

function clearHistory() {
    commandHistory = [];
    historyIndex = -1;
    updateHistoryDisplay();
}

function showAllowedCommands() {
    $.get('/allowed-commands')
        .done(function(data) {
            const commands = data.commands.join(', ');
            alert(`Allowed commands:\n\n${commands}`);
        })
        .fail(function() {
            alert('Failed to load allowed commands');
        });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
