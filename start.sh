#!/bin/bash

# Flask Web Terminal Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Flask Web Terminal Startup${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if Python is installed
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.7 or higher."
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_status "Python version: $python_version"
}

# Check if pip is installed
check_pip() {
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 is not installed. Please install pip3."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        print_status "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Set default environment variables
set_environment() {
    export FLASK_ENV=${FLASK_ENV:-development}
    export ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
    export PORT=${PORT:-8000}
    export HOST=${HOST:-0.0.0.0}
    
    if [ "$ADMIN_PASSWORD" = "admin123" ]; then
        print_warning "Using default password 'admin123'. Set ADMIN_PASSWORD environment variable for security."
    fi
    
    print_status "Environment: $FLASK_ENV"
    print_status "Host: $HOST"
    print_status "Port: $PORT"
}

# Run tests
run_tests() {
    if [ "$1" = "--test" ] || [ "$1" = "-t" ]; then
        print_status "Running tests..."
        python3.11 test_app.py
        return $?
    fi
    return 0
}

# Start the application
start_app() {
    print_status "Starting Flask Web Terminal..."
    print_status "Access the terminal at: http://$HOST:$PORT"
    print_status "Press Ctrl+C to stop the server"
    echo ""
    
    # Check if run.py exists, otherwise use app.py
    if [ -f "run.py" ]; then
        python3.11 run.py
    elif [ -f "app.py" ]; then
        python3.11 app.py
    else
        print_error "Neither run.py nor app.py found"
        exit 1
    fi
}

# Main function
main() {
    print_header
    
    # Parse command line arguments
    case "$1" in
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --help, -h     Show this help message"
            echo "  --test, -t     Run tests before starting"
            echo "  --install, -i  Only install dependencies"
            echo "  --docker, -d   Show Docker commands"
            echo ""
            echo "Environment Variables:"
            echo "  FLASK_ENV      Set to 'production' for production mode"
            echo "  ADMIN_PASSWORD Set admin password (default: admin123)"
            echo "  PORT           Port to run on (default: 5000)"
            echo "  HOST           Host to bind to (default: 0.0.0.0)"
            exit 0
            ;;
        --install|-i)
            check_python
            check_pip
            install_dependencies
            print_status "Installation complete"
            exit 0
            ;;
        --docker|-d)
            echo "Docker commands:"
            echo ""
            echo "Build image:"
            echo "  docker build -t web-terminal ."
            echo ""
            echo "Run container:"
            echo "  docker run -p 5000:5000 -e ADMIN_PASSWORD=your_password web-terminal"
            echo ""
            echo "Using docker-compose:"
            echo "  docker-compose up -d"
            exit 0
            ;;
    esac
    
    # Run checks
    check_python
    check_pip
    
    # Install dependencies if needed
    if [ ! -d "venv" ] && [ ! -f ".dependencies_installed" ]; then
        install_dependencies
        touch .dependencies_installed
    fi
    
    # Set environment
    set_environment
    
    # Run tests if requested
    if ! run_tests "$1"; then
        print_error "Tests failed"
        exit 1
    fi
    
    # Start the application
    start_app
}

# Handle Ctrl+C gracefully
trap 'print_status "Shutting down..."; exit 0' INT

# Run main function
main "$@"
