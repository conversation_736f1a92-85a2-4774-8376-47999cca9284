#!/usr/bin/env python3
"""
Flask Web Terminal Application
Provides a web interface for running SSH commands on the local system.
"""

import os
import subprocess
import json
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', secrets.token_hex(32))

# Configuration - Allow most commands but block dangerous ones
BLOCKED_COMMANDS = [
    'rm', 'rmdir', 'mv', 'cp', 'dd', 'fdisk', 'mkfs', 'format',
    'shutdown', 'reboot', 'halt', 'poweroff', 'init', 'telinit',
    'passwd', 'chpasswd', 'usermod', 'userdel', 'groupdel',
    'chmod', 'chown', 'chgrp', 'su', 'sudo', 'visudo',
    'crontab', 'at', 'batch', 'systemctl', 'service'
]

# Dangerous patterns to block (regex)
BLOCKED_PATTERNS = [
    r'rm\s+-rf\s*/',           # rm -rf /
    r':\(\)\{.*\}',            # Fork bomb
    r'>\s*/dev/sd[a-z]',       # Writing to disk devices
    r'dd\s+.*of=/dev',         # DD to devices
    r'mkfs\.',                 # Format filesystem
    r'fdisk\s+/dev',           # Partition manipulation
    r'>\s*/etc/',              # Writing to /etc
    r'>\s*/boot/',             # Writing to /boot
    r'>\s*/sys/',              # Writing to /sys
    r'>\s*/proc/',             # Writing to /proc
]

# Allow most commands by default, but use blocklist approach
ALLOW_ALL_COMMANDS = os.environ.get('ALLOW_ALL_COMMANDS', 'true').lower() == 'true'

# Simple authentication (in production, use proper authentication)
ADMIN_PASSWORD_HASH = generate_password_hash(os.environ.get('ADMIN_PASSWORD', 'admin123'))

def is_authenticated():
    """Check if user is authenticated"""
    return session.get('authenticated', False)

def is_command_allowed(command):
    """Check if command is allowed using blocklist approach"""
    import re

    cmd_parts = command.strip().split()
    if not cmd_parts:
        return False

    base_command = cmd_parts[0]

    # If ALLOW_ALL_COMMANDS is False, use restrictive whitelist
    if not ALLOW_ALL_COMMANDS:
        # Restrictive whitelist for high-security environments
        safe_commands = [
            'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
            'cat', 'head', 'tail', 'grep', 'find', 'which', 'whereis',
            'uname', 'hostname', 'id', 'groups', 'env', 'printenv',
            'ping', 'curl', 'wget', 'dig', 'nslookup', 'traceroute',
            'git', 'ssh', 'scp', 'rsync', 'docker', 'kubectl'
        ]
        return base_command in safe_commands

    # Check blocked commands
    if base_command in BLOCKED_COMMANDS:
        return False

    # Check blocked patterns
    for pattern in BLOCKED_PATTERNS:
        if re.search(pattern, command, re.IGNORECASE):
            return False

    # Allow command if it passes all checks
    return True

def execute_command(command, timeout=30):
    """Execute a command safely with timeout"""
    try:
        # Security check
        if not is_command_allowed(command):
            if ALLOW_ALL_COMMANDS:
                error_msg = 'Command blocked for security reasons. Check blocked commands list.'
            else:
                error_msg = 'Command not in whitelist. Only safe commands are allowed in restricted mode.'
            return {
                'success': False,
                'error': error_msg,
                'output': '',
                'exit_code': 1
            }
        
        # Execute command
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.path.expanduser('~')
        )
        
        return {
            'success': True,
            'output': result.stdout,
            'error': result.stderr,
            'exit_code': result.returncode,
            'command': command,
            'timestamp': datetime.now().isoformat()
        }
        
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': f'Command timed out after {timeout} seconds',
            'output': '',
            'exit_code': 124
        }
    except Exception as e:
        logger.error(f"Error executing command '{command}': {str(e)}")
        return {
            'success': False,
            'error': f'Error executing command: {str(e)}',
            'output': '',
            'exit_code': 1
        }

@app.route('/')
def index():
    """Main terminal interface"""
    if not is_authenticated():
        return render_template('login.html')
    return render_template('terminal.html')

@app.route('/login', methods=['POST'])
def login():
    """Handle login"""
    password = request.form.get('password')
    if password and check_password_hash(ADMIN_PASSWORD_HASH, password):
        session['authenticated'] = True
        return jsonify({'success': True})
    return jsonify({'success': False, 'error': 'Invalid password'})

@app.route('/logout')
def logout():
    """Handle logout"""
    session.pop('authenticated', None)
    return render_template('login.html')

@app.route('/execute', methods=['POST'])
def execute():
    """Execute a command"""
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    command = data.get('command', '').strip()
    
    if not command:
        return jsonify({'success': False, 'error': 'No command provided'})
    
    # Log command execution
    logger.info(f"Executing command: {command}")
    
    result = execute_command(command)
    return jsonify(result)

@app.route('/allowed-commands')
def allowed_commands():
    """Get command policy information"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401

    if ALLOW_ALL_COMMANDS:
        return jsonify({
            'policy': 'blocklist',
            'message': 'Most commands are allowed except blocked ones',
            'blocked_commands': BLOCKED_COMMANDS,
            'blocked_patterns': BLOCKED_PATTERNS
        })
    else:
        safe_commands = [
            'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
            'cat', 'head', 'tail', 'grep', 'find', 'which', 'whereis',
            'uname', 'hostname', 'id', 'groups', 'env', 'printenv',
            'ping', 'curl', 'wget', 'dig', 'nslookup', 'traceroute',
            'git', 'ssh', 'scp', 'rsync', 'docker', 'kubectl'
        ]
        return jsonify({
            'policy': 'whitelist',
            'message': 'Only whitelisted commands are allowed',
            'commands': safe_commands
        })

@app.route('/system-info')
def system_info():
    """Get basic system information"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401
    
    info_commands = [
        ('hostname', 'hostname'),
        ('uptime', 'uptime'),
        ('whoami', 'whoami'),
        ('pwd', 'pwd'),
        ('uname', 'uname -a')
    ]
    
    system_info = {}
    for key, cmd in info_commands:
        result = execute_command(cmd)
        if result['success']:
            system_info[key] = result['output'].strip()
        else:
            system_info[key] = 'N/A'
    
    return jsonify(system_info)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Run the application
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    print(f"Starting Flask Web Terminal on port {port}")
    print(f"Default admin password: admin123 (change via ADMIN_PASSWORD env var)")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
