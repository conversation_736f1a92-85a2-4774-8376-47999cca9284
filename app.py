#!/usr/bin/env python3
"""
Flask Web Terminal Application
Provides a web interface for running SSH commands on the local system.
"""

import os
import subprocess
import json
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', secrets.token_hex(32))

# Configuration
ALLOWED_COMMANDS = [
    'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
    'netstat', 'ss', 'top', 'htop', 'cat', 'head', 'tail', 'grep',
    'find', 'which', 'whereis', 'uname', 'hostname', 'id', 'groups',
    'history', 'env', 'printenv', 'mount', 'lsblk', 'lscpu', 'lsusb',
    'systemctl', 'service', 'journalctl', 'dmesg', 'lsof', 'iptables',
    'ip', 'ifconfig', 'ping', 'traceroute', 'nslookup', 'dig', 'curl',
    'wget', 'ssh', 'scp', 'rsync', 'git', 'docker', 'kubectl'
]

# Simple authentication (in production, use proper authentication)
ADMIN_PASSWORD_HASH = generate_password_hash(os.environ.get('ADMIN_PASSWORD', 'admin123'))

def is_authenticated():
    """Check if user is authenticated"""
    return session.get('authenticated', False)

def is_command_allowed(command):
    """Check if command is in the allowed list"""
    cmd_parts = command.strip().split()
    if not cmd_parts:
        return False
    
    base_command = cmd_parts[0]
    return base_command in ALLOWED_COMMANDS

def execute_command(command, timeout=30):
    """Execute a command safely with timeout"""
    try:
        # Security check
        if not is_command_allowed(command):
            return {
                'success': False,
                'error': f'Command not allowed. Allowed commands: {", ".join(ALLOWED_COMMANDS)}',
                'output': '',
                'exit_code': 1
            }
        
        # Execute command
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.path.expanduser('~')
        )
        
        return {
            'success': True,
            'output': result.stdout,
            'error': result.stderr,
            'exit_code': result.returncode,
            'command': command,
            'timestamp': datetime.now().isoformat()
        }
        
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': f'Command timed out after {timeout} seconds',
            'output': '',
            'exit_code': 124
        }
    except Exception as e:
        logger.error(f"Error executing command '{command}': {str(e)}")
        return {
            'success': False,
            'error': f'Error executing command: {str(e)}',
            'output': '',
            'exit_code': 1
        }

@app.route('/')
def index():
    """Main terminal interface"""
    if not is_authenticated():
        return render_template('login.html')
    return render_template('terminal.html')

@app.route('/login', methods=['POST'])
def login():
    """Handle login"""
    password = request.form.get('password')
    if password and check_password_hash(ADMIN_PASSWORD_HASH, password):
        session['authenticated'] = True
        return jsonify({'success': True})
    return jsonify({'success': False, 'error': 'Invalid password'})

@app.route('/logout')
def logout():
    """Handle logout"""
    session.pop('authenticated', None)
    return render_template('login.html')

@app.route('/execute', methods=['POST'])
def execute():
    """Execute a command"""
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    command = data.get('command', '').strip()
    
    if not command:
        return jsonify({'success': False, 'error': 'No command provided'})
    
    # Log command execution
    logger.info(f"Executing command: {command}")
    
    result = execute_command(command)
    return jsonify(result)

@app.route('/allowed-commands')
def allowed_commands():
    """Get list of allowed commands"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401
    
    return jsonify({'commands': ALLOWED_COMMANDS})

@app.route('/system-info')
def system_info():
    """Get basic system information"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401
    
    info_commands = [
        ('hostname', 'hostname'),
        ('uptime', 'uptime'),
        ('whoami', 'whoami'),
        ('pwd', 'pwd'),
        ('uname', 'uname -a')
    ]
    
    system_info = {}
    for key, cmd in info_commands:
        result = execute_command(cmd)
        if result['success']:
            system_info[key] = result['output'].strip()
        else:
            system_info[key] = 'N/A'
    
    return jsonify(system_info)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Run the application
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    print(f"Starting Flask Web Terminal on port {port}")
    print(f"Default admin password: admin123 (change via ADMIN_PASSWORD env var)")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
