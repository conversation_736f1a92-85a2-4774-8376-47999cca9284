#!/usr/bin/env python3
"""
Flask Web Terminal Application
Provides a web interface for running SSH commands on the local system.
"""

import os
import subprocess
import json
import logging
import threading
import queue
import time
import pty
import select
import termios
import fcntl
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', secrets.token_hex(32))

# Configuration - Allow most commands but block dangerous ones
BLOCKED_COMMANDS = [
    'rm', 'rmdir', 'mv', 'cp', 'dd', 'fdisk', 'mkfs', 'format',
    'shutdown', 'reboot', 'halt', 'poweroff', 'init', 'telinit',
    'passwd', 'chpasswd', 'usermod', 'userdel', 'groupdel',
    'chmod', 'chown', 'chgrp', 'su', 'sudo', 'visudo',
    'crontab', 'at', 'batch', 'systemctl', 'service'
]

# Dangerous patterns to block (regex)
BLOCKED_PATTERNS = [
    r'rm\s+-rf\s*/',           # rm -rf /
    r':\(\)\{.*\}',            # Fork bomb
    r'>\s*/dev/sd[a-z]',       # Writing to disk devices
    r'dd\s+.*of=/dev',         # DD to devices
    r'mkfs\.',                 # Format filesystem
    r'fdisk\s+/dev',           # Partition manipulation
    r'>\s*/etc/',              # Writing to /etc
    r'>\s*/boot/',             # Writing to /boot
    r'>\s*/sys/',              # Writing to /sys
    r'>\s*/proc/',             # Writing to /proc
]

# Allow most commands by default, but use blocklist approach
ALLOW_ALL_COMMANDS = os.environ.get('ALLOW_ALL_COMMANDS', 'true').lower() == 'true'

# Simple authentication (in production, use proper authentication)
ADMIN_PASSWORD_HASH = generate_password_hash(os.environ.get('ADMIN_PASSWORD', 'admin123'))

class ShellSession:
    """Manages a persistent shell session for each user"""

    def __init__(self):
        self.process = None
        self.current_dir = os.path.expanduser('~')
        self.env = os.environ.copy()
        self.start_shell()

    def start_shell(self):
        """Start a new shell process"""
        try:
            # Start bash in interactive mode
            self.process = subprocess.Popen(
                ['/bin/bash', '-i'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=0,
                cwd=self.current_dir,
                env=self.env,
                preexec_fn=os.setsid  # Create new process group
            )

            # Set non-blocking mode for stdout
            fd = self.process.stdout.fileno()
            fl = fcntl.fcntl(fd, fcntl.F_GETFL)
            fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)

            # Wait for shell to be ready and consume initial output
            time.sleep(0.1)
            self._read_available_output()

        except Exception as e:
            logger.error(f"Failed to start shell: {e}")
            self.process = None

    def _read_available_output(self, timeout=0.5):
        """Read all available output from the shell"""
        if not self.process or self.process.poll() is not None:
            return ""

        output = ""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Use select to check if data is available
                ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                if ready:
                    chunk = self.process.stdout.read(4096)
                    if chunk:
                        output += chunk
                    else:
                        break
                else:
                    # No more data available
                    if output:
                        break
            except (OSError, IOError):
                break

        return output

    def execute_command(self, command, timeout=30):
        """Execute a command in the persistent shell"""
        if not self.process or self.process.poll() is not None:
            self.start_shell()
            if not self.process:
                return {
                    'success': False,
                    'error': 'Failed to start shell session',
                    'output': '',
                    'exit_code': 1
                }

        try:
            # Send command to shell
            self.process.stdin.write(command + '\n')
            self.process.stdin.flush()

            # Add a unique marker to detect end of output
            marker = f"__END_COMMAND_{int(time.time() * 1000000)}__"
            self.process.stdin.write(f'echo "{marker}"\n')
            self.process.stdin.flush()

            # Read output until we see the marker
            output = ""
            start_time = time.time()

            while time.time() - start_time < timeout:
                chunk = self._read_available_output(0.1)
                if chunk:
                    output += chunk
                    if marker in output:
                        # Remove the marker and everything after it
                        output = output.split(marker)[0].strip()
                        break

                # Check if process died
                if self.process.poll() is not None:
                    break

            # Update current directory by checking pwd
            try:
                self.process.stdin.write('pwd\n')
                self.process.stdin.flush()
                pwd_marker = f"__PWD_{int(time.time() * 1000000)}__"
                self.process.stdin.write(f'echo "{pwd_marker}"\n')
                self.process.stdin.flush()

                pwd_output = ""
                pwd_start = time.time()
                while time.time() - pwd_start < 2:
                    chunk = self._read_available_output(0.1)
                    if chunk:
                        pwd_output += chunk
                        if pwd_marker in pwd_output:
                            pwd_lines = pwd_output.split(pwd_marker)[0].strip().split('\n')
                            if pwd_lines:
                                self.current_dir = pwd_lines[-1].strip()
                            break
            except:
                pass  # Don't fail if we can't get pwd

            return {
                'success': True,
                'output': output,
                'error': '',
                'exit_code': 0,
                'command': command,
                'timestamp': datetime.now().isoformat(),
                'current_dir': self.current_dir
            }

        except Exception as e:
            logger.error(f"Error executing command '{command}': {str(e)}")
            return {
                'success': False,
                'error': f'Error executing command: {str(e)}',
                'output': '',
                'exit_code': 1
            }

    def close(self):
        """Close the shell session"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            self.process = None

# Global dictionary to store shell sessions per user session
shell_sessions = {}

def is_authenticated():
    """Check if user is authenticated"""
    return session.get('authenticated', False)

def is_command_allowed(command):
    """Check if command is allowed using blocklist approach"""
    import re

    cmd_parts = command.strip().split()
    if not cmd_parts:
        return False

    base_command = cmd_parts[0]

    # If ALLOW_ALL_COMMANDS is False, use restrictive whitelist
    if not ALLOW_ALL_COMMANDS:
        # Restrictive whitelist for high-security environments
        safe_commands = [
            'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
            'cat', 'head', 'tail', 'grep', 'find', 'which', 'whereis',
            'uname', 'hostname', 'id', 'groups', 'env', 'printenv',
            'ping', 'curl', 'wget', 'dig', 'nslookup', 'traceroute',
            'git', 'ssh', 'scp', 'rsync', 'docker', 'kubectl'
        ]
        return base_command in safe_commands

    # Check blocked commands
    if base_command in BLOCKED_COMMANDS:
        return False

    # Check blocked patterns
    for pattern in BLOCKED_PATTERNS:
        if re.search(pattern, command, re.IGNORECASE):
            return False

    # Allow command if it passes all checks
    return True

def get_shell_session():
    """Get or create a shell session for the current user session"""
    session_id = session.get('session_id')
    if not session_id:
        session_id = secrets.token_hex(16)
        session['session_id'] = session_id

    if session_id not in shell_sessions:
        shell_sessions[session_id] = ShellSession()

    return shell_sessions[session_id]

def execute_command(command, timeout=30):
    """Execute a command in the persistent shell session"""
    try:
        # Security check
        if not is_command_allowed(command):
            if ALLOW_ALL_COMMANDS:
                error_msg = 'Command blocked for security reasons. Check blocked commands list.'
            else:
                error_msg = 'Command not in whitelist. Only safe commands are allowed in restricted mode.'
            return {
                'success': False,
                'error': error_msg,
                'output': '',
                'exit_code': 1
            }

        # Get the shell session for this user
        shell_session = get_shell_session()

        # Execute command in persistent shell
        result = shell_session.execute_command(command, timeout)

        return result

    except Exception as e:
        logger.error(f"Error executing command '{command}': {str(e)}")
        return {
            'success': False,
            'error': f'Error executing command: {str(e)}',
            'output': '',
            'exit_code': 1
        }

@app.route('/')
def index():
    """Main terminal interface"""
    if not is_authenticated():
        return render_template('login.html')
    return render_template('terminal.html')

@app.route('/login', methods=['POST'])
def login():
    """Handle login"""
    password = request.form.get('password')
    if password and check_password_hash(ADMIN_PASSWORD_HASH, password):
        session['authenticated'] = True
        return jsonify({'success': True})
    return jsonify({'success': False, 'error': 'Invalid password'})

@app.route('/logout')
def logout():
    """Handle logout"""
    # Clean up shell session
    session_id = session.get('session_id')
    if session_id and session_id in shell_sessions:
        shell_sessions[session_id].close()
        del shell_sessions[session_id]

    session.clear()
    return render_template('login.html')

@app.route('/execute', methods=['POST'])
def execute():
    """Execute a command"""
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    command = data.get('command', '').strip()
    
    if not command:
        return jsonify({'success': False, 'error': 'No command provided'})
    
    # Log command execution
    logger.info(f"Executing command: {command}")
    
    result = execute_command(command)
    return jsonify(result)

@app.route('/allowed-commands')
def allowed_commands():
    """Get command policy information"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401

    if ALLOW_ALL_COMMANDS:
        return jsonify({
            'policy': 'blocklist',
            'message': 'Most commands are allowed except blocked ones',
            'blocked_commands': BLOCKED_COMMANDS,
            'blocked_patterns': BLOCKED_PATTERNS
        })
    else:
        safe_commands = [
            'ls', 'pwd', 'whoami', 'date', 'uptime', 'df', 'free', 'ps',
            'cat', 'head', 'tail', 'grep', 'find', 'which', 'whereis',
            'uname', 'hostname', 'id', 'groups', 'env', 'printenv',
            'ping', 'curl', 'wget', 'dig', 'nslookup', 'traceroute',
            'git', 'ssh', 'scp', 'rsync', 'docker', 'kubectl'
        ]
        return jsonify({
            'policy': 'whitelist',
            'message': 'Only whitelisted commands are allowed',
            'commands': safe_commands
        })

@app.route('/current-dir')
def current_dir():
    """Get current working directory"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        shell_session = get_shell_session()
        return jsonify({
            'current_dir': shell_session.current_dir,
            'user': os.environ.get('USER', 'user'),
            'hostname': os.uname().nodename
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/system-info')
def system_info():
    """Get basic system information"""
    if not is_authenticated():
        return jsonify({'error': 'Not authenticated'}), 401

    info_commands = [
        ('hostname', 'hostname'),
        ('uptime', 'uptime'),
        ('whoami', 'whoami'),
        ('pwd', 'pwd'),
        ('uname', 'uname -a')
    ]

    system_info = {}
    for key, cmd in info_commands:
        result = execute_command(cmd)
        if result['success']:
            system_info[key] = result['output'].strip()
        else:
            system_info[key] = 'N/A'

    return jsonify(system_info)

def cleanup_shell_sessions():
    """Clean up all shell sessions on shutdown"""
    for _, shell_session in shell_sessions.items():
        try:
            shell_session.close()
        except:
            pass
    shell_sessions.clear()

import atexit
atexit.register(cleanup_shell_sessions)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    # Run the application
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    print(f"Starting Flask Web Terminal on port {port}")
    print(f"Default admin password: admin123 (change via ADMIN_PASSWORD env var)")
    print(f"Shell sessions will persist across commands")

    try:
        app.run(host='0.0.0.0', port=port, debug=debug)
    except KeyboardInterrupt:
        print("\nShutting down...")
        cleanup_shell_sessions()
    except Exception as e:
        print(f"Error: {e}")
        cleanup_shell_sessions()
